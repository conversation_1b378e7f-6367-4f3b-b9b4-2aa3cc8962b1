import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Warning: This allows production builds to successfully complete even if
    // your project has type errors.
    ignoreBuildErrors: true,
  },
  // Use serverExternalPackages for Next.js 15+
  serverExternalPackages: [
    'metascraper',
    'metascraper-logo',
    'metascraper-logo-favicon',
    'metascraper-image',
    're2',
    'cheerio',
    'undici'
  ],
  webpack: (config, { isServer }) => {
    // Exclude native modules from client-side bundling
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        // Exclude native modules that should only run server-side
        're2': false,
        'fsevents': false,
        'cpu-features': false,
        // Additional metascraper dependencies
        'metascraper': false,
        'metascraper-logo': false,
        'metascraper-logo-favicon': false,
        'metascraper-image': false,
        'cheerio': false,
        'undici': false,
      };

      // Exclude native binary files from client bundle
      config.module.rules.push({
        test: /\.node$/,
        use: 'ignore-loader',
      });

      // Exclude metascraper packages completely from client bundle
      config.externals = config.externals || [];
      config.externals.push(
        'metascraper',
        'metascraper-logo',
        'metascraper-logo-favicon',
        'metascraper-image',
        're2',
        'cheerio',
        'undici'
      );
    }

    return config;
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'picsum.photos',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'placehold.co',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.jsdelivr.net',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'raw.githubusercontent.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
};

export default nextConfig;
