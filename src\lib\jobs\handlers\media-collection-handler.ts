/**
 * Media Collection Job Handler
 * 
 * Handles media collection jobs using metascraper for efficient favicon and OG image extraction.
 * Runs as a separate background job after content generation completes.
 */

import { Job, JobHandler } from '../types';

export class MediaCollectionHandler implements JobHandler {
  /**
   * Handle media collection job
   * Uses dynamic import to prevent webpack bundling issues with native modules
   */
  async handle(job: Job): Promise<any> {
    console.log(`📸 Processing media collection job ${job.id}`);

    try {
      // Dynamic import to prevent webpack from bundling MediaCollectionJob for client-side
      console.log(`🔍 Loading MediaCollectionJob for server-side execution...`);
      const { MediaCollectionJob } = await import('../media-collection-job');

      // Create instance and handle job
      const mediaCollectionJob = new MediaCollectionJob();
      const result = await mediaCollectionJob.handle(job);

      console.log(`✅ Media collection job ${job.id} completed successfully`);
      return result;

    } catch (error) {
      console.error(`❌ Media collection job ${job.id} failed:`, error);
      throw error;
    }
  }
}

export default MediaCollectionHandler;
