-- Migration 009: Add media_collection job type to ai_generation_jobs constraint
-- This migration updates the job_type constraint to include 'media_collection'
-- which is used by the MediaCollectionJob system for favicon and OG image extraction

-- =====================================================
-- UPDATE JOB TYPE CONSTRAINT
-- =====================================================

-- Drop the existing constraint
ALTER TABLE ai_generation_jobs DROP CONSTRAINT IF EXISTS ai_generation_jobs_job_type_check_v3;

-- Add the updated constraint with 'media_collection' included
ALTER TABLE ai_generation_jobs ADD CONSTRAINT ai_generation_jobs_job_type_check_v4 
CHECK (job_type IN (
    'tool_submission',
    'content_generation', 
    'web_scraping',
    'email_notification',
    'tool_processing',
    'screenshot_capture',
    'favicon_extraction',
    'bulk_processing',
    'media_collection',  -- NEW: Added for MediaCollectionJob system
    -- Legacy values for backward compatibility
    'scrape',
    'generate', 
    'bulk',
    'media_extraction'
));

-- =====================================================
-- VERIFICATION TESTS
-- =====================================================

-- Test media_collection job type insertion
DO $$
DECLARE
    test_uuid UUID := gen_random_uuid();
    test_tool_id VARCHAR(255) := '337dd2cc-f957-4bf6-94af-f7bc96f9a9d8';
BEGIN
    -- Test media_collection job type
    INSERT INTO ai_generation_jobs (
        id, 
        tool_id,
        job_type, 
        status, 
        progress, 
        attempts, 
        max_attempts, 
        priority,
        job_data
    ) VALUES (
        test_uuid, 
        test_tool_id,
        'media_collection', 
        'pending', 
        0, 
        0, 
        3, 
        2,
        '{"url": "https://www.foodieprep.ai/", "toolId": "337dd2cc-f957-4bf6-94af-f7bc96f9a9d8"}'::jsonb
    );
    
    RAISE NOTICE 'SUCCESS: media_collection job type accepted';
    
    -- Clean up test record
    DELETE FROM ai_generation_jobs WHERE id = test_uuid;
    
EXCEPTION
    WHEN check_violation THEN
        RAISE EXCEPTION 'FAILED: media_collection job type still rejected by constraint';
    WHEN OTHERS THEN
        RAISE NOTICE 'Test completed with other error (may be expected): %', SQLERRM;
        -- Clean up test record if it exists
        DELETE FROM ai_generation_jobs WHERE id = test_uuid;
END $$;

-- =====================================================
-- CONSTRAINT DOCUMENTATION
-- =====================================================

COMMENT ON CONSTRAINT ai_generation_jobs_job_type_check_v4 ON ai_generation_jobs 
IS 'Ensures job_type matches TypeScript JobType enum values including media_collection for favicon/OG image extraction';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check the updated constraint
SELECT
    conname as constraint_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint
WHERE conname = 'ai_generation_jobs_job_type_check_v4';

-- Show all allowed job types
SELECT 
    'ai_generation_jobs_job_type_check_v4' as constraint_name,
    unnest(ARRAY[
        'tool_submission',
        'content_generation', 
        'web_scraping',
        'email_notification',
        'tool_processing',
        'screenshot_capture',
        'favicon_extraction',
        'bulk_processing',
        'media_collection',
        'scrape',
        'generate', 
        'bulk',
        'media_extraction'
    ]) as allowed_job_type;

-- =====================================================
-- MIGRATION COMPLETION
-- =====================================================

-- Log successful migration
DO $$
BEGIN
    RAISE NOTICE '✅ Migration 009 completed successfully';
    RAISE NOTICE '✅ Added media_collection job type to ai_generation_jobs constraint';
    RAISE NOTICE '✅ MediaCollectionJob system can now create database records';
    RAISE NOTICE '✅ Constraint updated from v3 to v4';
END $$;
