import { JobType, <PERSON><PERSON><PERSON><PERSON> } from '../types';
import { ToolSubmissionHandler } from './tool-submission';
import { ContentGenerationHandler } from './content-generation';
import { WebScrapingHandler } from './web-scraping';
import { EmailNotificationHandler } from './email-notification';
import { BulkProcessingHandler } from './bulk-processing';

// Cache for lazy-loaded handlers
const handlerCache = new Map<JobType, JobHandler>();

// Static handlers (safe to import)
const staticHandlers: Partial<Record<JobType, JobHandler>> = {
  [JobType.TOOL_SUBMISSION]: new ToolSubmissionHandler(),
  [JobType.CONTENT_GENERATION]: new ContentGenerationHandler(),
  [JobType.WEB_SCRAPING]: new WebScrapingHandler(),
  [JobType.EMAIL_NOTIFICATION]: new EmailNotificationHandler(),
  [JobType.TOOL_PROCESSING]: new ToolSubmissionHandler(), // Alias for tool submission
  [JobType.SCREENSHOT_CAPTURE]: new WebScrapingHandler(), // Part of web scraping
  [JobType.FAVICON_EXTRACTION]: new WebScrapingHandler(), // Part of web scraping
  [JobType.BULK_PROCESSING]: new BulkProcessingHandler(),
};

export async function getJobHandler(type: JobType): Promise<JobHandler> {
  // Check cache first
  if (handlerCache.has(type)) {
    return handlerCache.get(type)!;
  }

  // Check static handlers
  if (staticHandlers[type]) {
    const handler = staticHandlers[type]!;
    handlerCache.set(type, handler);
    return handler;
  }

  // Lazy-load handlers that have native dependencies
  if (type === JobType.MEDIA_COLLECTION) {
    console.log('🔍 Lazy-loading MediaCollectionHandler to prevent webpack bundling issues...');
    const { MediaCollectionHandler } = await import('./media-collection-handler');
    const handler = new MediaCollectionHandler();
    handlerCache.set(type, handler);
    return handler;
  }

  throw new Error(`No handler found for job type: ${type}`);
}

export * from './tool-submission';
export * from './content-generation';
export * from './web-scraping';
export * from './email-notification';
