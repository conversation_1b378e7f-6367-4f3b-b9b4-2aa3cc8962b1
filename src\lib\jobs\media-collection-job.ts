/**
 * Media Collection Job
 *
 * Dedicated background job for collecting media assets (favicon, OG images)
 * Runs after content generation completes, separate from enhanced scraping.
 * Uses metascraper for efficient media extraction without screenshots.
 */

import { Job } from './types';
import { createClient } from '@supabase/supabase-js';

// Type for metascraper metadata result
interface MetascraperMetadata {
  logo?: string;
  image?: string;
  title?: string;
  description?: string;
  [key: string]: unknown;
}

// Type for database update data
interface ToolUpdateData {
  logo_url?: string;
  screenshots?: string[];
}

// Type for job manager (simplified interface)
interface JobManager {
  createJob(type: string, data: unknown, options?: unknown): Promise<{ id: string }>;
}

interface MediaCollectionJobData {
  toolId: string;
  url: string;
  options?: {
    collectFavicon?: boolean;
    collectOgImages?: boolean;
    forceCollection?: boolean; // Override existing media
  };
}

interface MediaCollectionResult {
  favicon?: string | null;
  ogImages?: string[];
  logoUrl?: string | null;
  success: boolean;
  error?: string;
  metadata?: {
    collectionMethod: string;
    timestamp: string;
    creditsUsed: number;
  };
}

export class MediaCollectionJob {
  private supabaseAdmin;

  constructor() {
    this.supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );
  }

  /**
   * Main job handler for media collection
   */
  async handle(job: Job): Promise<MediaCollectionResult> {
    const { toolId, url, options = {} } = job.data as MediaCollectionJobData;
    
    console.log(`📸 Starting media collection job for tool ${toolId} at ${url}`);
    
    try {
      // Check if tool exists and get current media status
      const { data: tool, error: toolError } = await this.supabaseAdmin
        .from('tools')
        .select('id, name, logo_url, screenshots')
        .eq('id', toolId)
        .single();

      if (toolError || !tool) {
        throw new Error(`Tool not found: ${toolId}`);
      }

      // Skip if media already exists and not forcing collection
      if (!options.forceCollection && tool.logo_url && tool.screenshots?.length > 0) {
        console.log(`📸 Media already exists for tool ${toolId}, skipping collection`);
        return {
          success: true,
          favicon: tool.logo_url,
          ogImages: tool.screenshots || [],
          metadata: {
            collectionMethod: 'skipped_existing',
            timestamp: new Date().toISOString(),
            creditsUsed: 0
          }
        };
      }

      // Collect media using metascraper
      const mediaResult = await this.collectMediaWithMetascraper(url, options);

      // Update tool with collected media
      await this.updateToolMedia(toolId, mediaResult);

      console.log(`✅ Media collection completed for tool ${toolId}`);
      
      return {
        ...mediaResult,
        success: true,
        metadata: {
          collectionMethod: 'metascraper',
          timestamp: new Date().toISOString(),
          creditsUsed: 0 // Metascraper doesn't use external API credits
        }
      };

    } catch (error) {
      console.error(`❌ Media collection failed for tool ${toolId}:`, error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          collectionMethod: 'failed',
          timestamp: new Date().toISOString(),
          creditsUsed: 0
        }
      };
    }
  }

  /**
   * Collect media using metascraper (no external API costs)
   */
  private async collectMediaWithMetascraper(
    url: string,
    options: MediaCollectionJobData['options'] = {}
  ): Promise<Partial<MediaCollectionResult>> {
    const { collectFavicon = true, collectOgImages = true } = options;

    try {
      // Fetch the page content
      console.log(`🔍 Fetching page content for media extraction: ${url}`);

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch page: ${response.status} ${response.statusText}`);
      }

      const html = await response.text();
      const pageUrl = response.url; // Handle redirects

      // Dynamic import of metascraper (server-side only)
      console.log(`🔍 Loading metascraper for media extraction...`);
      const [
        metascraper,
        metascraperLogo,
        metascraperLogoFavicon,
        metascraperImage
      ] = await Promise.all([
        import('metascraper').then(m => m.default),
        import('metascraper-logo').then(m => m.default),
        import('metascraper-logo-favicon').then(m => m.default),
        import('metascraper-image').then(m => m.default)
      ]);

      // Initialize metascraper with media-focused plugins
      const scraper = metascraper([
        metascraperLogo(),
        metascraperLogoFavicon(),
        metascraperImage()
      ]);

      // Extract metadata using metascraper
      const metadata = await scraper({ html, url: pageUrl });

      const result: Partial<MediaCollectionResult> = {};

      // Collect favicon/logo
      if (collectFavicon) {
        result.favicon = await this.extractFavicon(url, metadata);
        result.logoUrl = result.favicon; // Use favicon as logo
      }

      // Collect Open Graph images
      if (collectOgImages) {
        result.ogImages = this.extractOgImages(metadata);
      }

      console.log(`📸 Media extraction completed:`, {
        favicon: !!result.favicon,
        ogImages: result.ogImages?.length || 0
      });

      return result;

    } catch (error) {
      console.error('Metascraper media collection failed:', error);
      
      // Fallback to basic favicon extraction
      if (collectFavicon) {
        const fallbackFavicon = await this.extractFaviconFallback(url);
        return {
          favicon: fallbackFavicon,
          logoUrl: fallbackFavicon,
          ogImages: collectOgImages ? [] : undefined
        };
      }

      throw error;
    }
  }

  /**
   * Extract favicon using metascraper with fallbacks
   */
  private async extractFavicon(url: string, metadata: MetascraperMetadata): Promise<string | null> {
    // Try metascraper logo first
    if (metadata.logo) {
      console.log(`📸 Found logo via metascraper: ${metadata.logo}`);
      return metadata.logo;
    }

    // Fallback to standard favicon.ico
    return await this.extractFaviconFallback(url);
  }

  /**
   * Fallback favicon extraction (domain.com/favicon.ico)
   */
  private async extractFaviconFallback(url: string): Promise<string | null> {
    try {
      const domain = new URL(url).origin;
      const faviconUrl = `${domain}/favicon.ico`;
      
      console.log(`📸 Trying fallback favicon: ${faviconUrl}`);
      
      // Test if favicon exists
      const response = await fetch(faviconUrl, { method: 'HEAD' });
      
      if (response.ok) {
        console.log(`✅ Found favicon: ${faviconUrl}`);
        return faviconUrl;
      }
    } catch (error) {
      console.log(`❌ Favicon fallback failed: ${error}`);
    }

    return null;
  }

  /**
   * Extract Open Graph images from metadata
   */
  private extractOgImages(metadata: MetascraperMetadata): string[] {
    const images: string[] = [];

    // Extract from metascraper image field
    if (metadata.image) {
      images.push(metadata.image);
    }

    // Remove duplicates and filter valid URLs
    return [...new Set(images)].filter(img => {
      try {
        new URL(img);
        return true;
      } catch {
        return false;
      }
    });
  }

  /**
   * Update tool with collected media assets
   */
  private async updateToolMedia(
    toolId: string,
    mediaResult: Partial<MediaCollectionResult>
  ): Promise<void> {
    const updateData: ToolUpdateData = {};

    // Update logo_url if favicon was collected
    if (mediaResult.favicon) {
      updateData.logo_url = mediaResult.favicon;
    }

    // Update screenshots if OG images were collected
    if (mediaResult.ogImages && mediaResult.ogImages.length > 0) {
      updateData.screenshots = mediaResult.ogImages;
    }

    // Only update if we have data to update
    if (Object.keys(updateData).length > 0) {
      const { error } = await this.supabaseAdmin
        .from('tools')
        .update(updateData)
        .eq('id', toolId);

      if (error) {
        throw new Error(`Failed to update tool media: ${error.message}`);
      }

      console.log(`📸 Updated tool ${toolId} with media:`, updateData);
    } else {
      console.log(`📸 No media collected for tool ${toolId}`);
    }
  }

  /**
   * Create media collection job
   */
  static async createJob(
    jobManager: JobManager,
    toolId: string,
    url: string,
    options: MediaCollectionJobData['options'] = {}
  ): Promise<string> {
    const job = await jobManager.createJob(
      'MEDIA_COLLECTION',
      {
        toolId,
        url,
        options: {
          collectFavicon: true,
          collectOgImages: true,
          ...options
        }
      },
      {
        priority: 'NORMAL',
        delay: 0 // Run immediately after content generation
      }
    );

    console.log(`📸 Media collection job ${job.id} created for tool ${toolId}`);
    return job.id;
  }

  /**
   * Trigger media collection for existing tools (manual/bulk)
   */
  static async triggerForTool(
    jobManager: JobManager,
    toolId: string,
    forceCollection = false
  ): Promise<string> {
    // Get tool URL from database
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    const { data: tool, error } = await supabaseAdmin
      .from('tools')
      .select('website')
      .eq('id', toolId)
      .single();

    if (error || !tool?.website) {
      throw new Error(`Tool not found or missing website: ${toolId}`);
    }

    return await MediaCollectionJob.createJob(
      jobManager,
      toolId,
      tool.website,
      { forceCollection }
    );
  }
}

export default MediaCollectionJob;
